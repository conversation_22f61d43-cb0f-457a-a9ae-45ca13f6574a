/* 底部导航栏美化样式 - 医生蓝色主题 */

/* 底部导航栏容器样式 */
.uni-tabbar {
    background: linear-gradient(135deg, #ffffff 0%, #f8fbff 100%) !important;
    border-top: 1px solid #e8f4fd !important;
    box-shadow: 0 -2px 12px rgba(74, 144, 226, 0.08) !important;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* 导航项容器 */
.uni-tabbar-item {
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 导航项悬停效果 */
.uni-tabbar-item:active {
    transform: scale(0.95);
}

/* 图标容器 */
.uni-tabbar-item .uni-tabbar-item-icon {
    position: relative;
    transition: all 0.3s ease;
}

/* 选中状态的图标容器 */
.uni-tabbar-item.uni-tabbar-item-selected .uni-tabbar-item-icon {
    transform: translateY(-2px);
}

/* 选中状态的背景光晕效果 */
.uni-tabbar-item.uni-tabbar-item-selected .uni-tabbar-item-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.1;
    z-index: -1;
    animation: pulse 2s infinite;
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.1;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 0.15;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 0.1;
    }
}

/* 文字样式 */
.uni-tabbar-item .uni-tabbar-item-text {
    font-size: 11px !important;
    font-weight: 500;
    letter-spacing: 0.3px;
    transition: all 0.3s ease;
}

/* 选中状态的文字 */
.uni-tabbar-item.uni-tabbar-item-selected .uni-tabbar-item-text {
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(74, 144, 226, 0.2);
}

/* 未选中状态的颜色 */
.uni-tabbar-item:not(.uni-tabbar-item-selected) .uni-tabbar-item-text {
    color: #8A8A8A !important;
}

.uni-tabbar-item:not(.uni-tabbar-item-selected) .uni-tabbar-item-icon {
    opacity: 0.7;
}

/* 选中状态的颜色 */
.uni-tabbar-item.uni-tabbar-item-selected .uni-tabbar-item-text {
    color: #4A90E2 !important;
}

.uni-tabbar-item.uni-tabbar-item-selected .uni-tabbar-item-icon {
    opacity: 1;
}

/* 添加微妙的分隔线 */
.uni-tabbar-item:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 20%;
    height: 60%;
    width: 1px;
    background: linear-gradient(to bottom, transparent, #e8f4fd, transparent);
    opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .uni-tabbar-item .uni-tabbar-item-text {
        font-size: 10px !important;
    }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .uni-tabbar {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
        border-top: 1px solid #333 !important;
        box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.3) !important;
    }
    
    .uni-tabbar-item:not(.uni-tabbar-item-selected) .uni-tabbar-item-text {
        color: #999 !important;
    }
    
    .uni-tabbar-item:not(:last-child)::after {
        background: linear-gradient(to bottom, transparent, #333, transparent);
    }
}

/* 特殊页面的底部导航栏样式 */
.medical-theme .uni-tabbar {
    background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%) !important;
    border-top: 2px solid #4A90E2 !important;
}

.medical-theme .uni-tabbar-item.uni-tabbar-item-selected .uni-tabbar-item-icon::before {
    background: linear-gradient(135deg, #4A90E2 0%, #2E7BC6 100%);
    opacity: 0.15;
}

/* 图标大小调整 */
.uni-tabbar-item .uni-tabbar-item-icon image {
    width: 24px !important;
    height: 24px !important;
    transition: all 0.3s ease;
}

.uni-tabbar-item.uni-tabbar-item-selected .uni-tabbar-item-icon image {
    transform: scale(1.1);
}

/* 添加底部安全区域适配 */
.uni-tabbar {
    padding-bottom: env(safe-area-inset-bottom);
}

/* 增强医疗主题的专业感 */
.medical-professional .uni-tabbar {
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 50%, #e6f3ff 100%) !important;
    border-top: 2px solid #4A90E2 !important;
    box-shadow: 0 -4px 20px rgba(74, 144, 226, 0.12) !important;
}

/* 为每个导航项添加专业的医疗图标效果 */
.medical-professional .uni-tabbar-item.uni-tabbar-item-selected .uni-tabbar-item-icon::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    width: 20px;
    height: 2px;
    background: linear-gradient(90deg, #4A90E2, #357ABD);
    border-radius: 1px;
    transform: translateX(-50%);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        width: 0;
        opacity: 0;
    }
    to {
        width: 20px;
        opacity: 1;
    }
}

/* 添加医疗主题的渐变背景 */
.medical-gradient {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 50%, #2E7BC6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
